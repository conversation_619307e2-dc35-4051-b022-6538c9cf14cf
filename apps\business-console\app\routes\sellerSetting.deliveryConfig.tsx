
import { useState, useEffect } from "react"
import { But<PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Switch } from "~/components/ui/switch"
import { Trash2, Plus, Truck } from "lucide-react"
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useRevalidator } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { LoaderFunction } from "@remix-run/node"
import { useToast } from "~/components/ui/ToastProvider";
import { getDeliveryConfigs, createDcConfig, deleteDeliveryConfig } from "~/services/deliveryConfigService"
import { dclistingResponse, DcBody, ConfigType } from "~/types/api/businessConsoleService/DeliveryConfig"

interface SlabConfig {
  id: string;
  minOrderValue: number;
  maxOrderValue: number;
  buyerPercentage: number;
  sellerPercentage: number;
  maxBuyerDeliveryCharge: number;
  maxSellerDeliveryCharge: number;
  percentageOption: 'buyer-pays' | 'seller-pays' | 'custom';
}

interface Loaderdata {
  data: dclistingResponse[];
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const sellerId = user?.userDetails?.sellerId;
  try {
    if (!sellerId) {
      throw new Response("Seller ID is required", { status: 400 });
    }
    const response = await getDeliveryConfigs(sellerId, request);
    return withResponse({ data: response.data?.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId = user?.userDetails?.sellerId;

  if (!sellerId) {
    return json(
      { data: null, actionType: actionType, success: false, error: "Seller ID is required" },
      { status: 400 }
    );
  }

  if (actionType === "deleteDeliveryConfig") {
    const configId = Number(formData.get("configId"));
    try {
      const response = await deleteDeliveryConfig(configId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "updateStatus") {
    const config = JSON.parse(formData.get("config") as string);

    try {
      // Get current config data first, then update only the active status
      const payload: Partial<DcBody> = {
        ...config,
        active: !config.active
      };

      const response = await createDcConfig(payload, request, config.id);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "addDeliveryConfig") {
    const slabsData = formData.get("slabs");

    if (!slabsData) {
      return json(
        { data: null, actionType: actionType, success: false, error: "No delivery range data provided" },
        { status: 400 }
      );
    }

    try {
      const slabs: SlabConfig[] = JSON.parse(slabsData as string);

      // Create all configurations
      for (const slab of slabs) {
        const payload: Partial<DcBody> = {
          sellerId: sellerId,
          configType: ConfigType.PERCENTAGE_BASED, // Always use PERCENTAGE_BASED
          minOrderValue: slab.minOrderValue,
          maxOrderValue: slab.maxOrderValue,
          buyerPercentage: slab.buyerPercentage,
          sellerPercentage: slab.sellerPercentage,
          active: true
        };

        // Add max charges if specified
        if (slab.maxBuyerDeliveryCharge > 0) {
          payload.maxBuyerDeliveryCharge = slab.maxBuyerDeliveryCharge;
        }
        if (slab.maxSellerDeliveryCharge > 0) {
          payload.maxSellerDeliveryCharge = slab.maxSellerDeliveryCharge;
        }

        await createDcConfig(payload, request);
      }

      return json(
        { data: null, actionType: actionType, success: true },
        { status: 200 }
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});

// Component for individual delivery range configuration
function DeliveryRangeCard({
  slab,
  index,
  isLast,
  onUpdateMaxValue,
  onUpdatePercentage,
  onUpdateMaxCharge
}: {
  slab: SlabConfig;
  index: number;
  isLast: boolean;
  onUpdateMaxValue: (newMaxValue: number) => void;
  onUpdatePercentage: (option: 'buyer-pays' | 'seller-pays' | 'custom', customBuyer?: number) => void;
  onUpdateMaxCharge: (field: 'buyer' | 'seller', value: number) => void;
}) {
  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <CardTitle className="text-base">
          Delivery Range {index + 1}: ₹{slab.minOrderValue} - ₹{slab.maxOrderValue}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Order Range */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <Label className="text-sm font-medium">From (₹)</Label>
            <Input
              type="number"
              value={slab.minOrderValue}
              disabled
              className="mt-1 bg-gray-50"
            />
            <p className="text-xs text-gray-500 mt-1">
              {index === 0 ? "Always starts from ₹0" : "Auto-calculated"}
            </p>
          </div>
          <div>
            <Label className="text-sm font-medium">Up to (₹)</Label>
            <Input
              type="number"
              min={slab.minOrderValue + 1}
              max="10000"
              value={slab.maxOrderValue}
              onChange={(e) => onUpdateMaxValue(Number(e.target.value))}
              disabled={isLast && slab.maxOrderValue === 10000}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              {isLast ? "Must end at ₹10,000" : "Set less than ₹10,000 to add another range"}
            </p>
          </div>
        </div>

        {/* Cost Sharing Options */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Who pays delivery charges?</Label>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name={`percentage-${slab.id}`}
                checked={slab.percentageOption === 'buyer-pays'}
                onChange={() => onUpdatePercentage('buyer-pays')}
                className="text-blue-600"
              />
              <span className="text-sm">Customer pays 100% (You pay 0%)</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name={`percentage-${slab.id}`}
                checked={slab.percentageOption === 'seller-pays'}
                onChange={() => onUpdatePercentage('seller-pays')}
                className="text-blue-600"
              />
              <span className="text-sm">You pay 100% (Customer pays 0%)</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name={`percentage-${slab.id}`}
                checked={slab.percentageOption === 'custom'}
                onChange={() => onUpdatePercentage('custom')}
                className="text-blue-600"
              />
              <span className="text-sm">Custom split</span>
            </label>
          </div>

          {slab.percentageOption === 'custom' && (
            <div className="grid grid-cols-2 gap-3 mt-2">
              <div>
                <Label className="text-xs">Customer pays (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={slab.buyerPercentage}
                  onChange={(e) => onUpdatePercentage('custom', Number(e.target.value))}
                  className="mt-1"
                />
              </div>
              <div>
                <Label className="text-xs">You pay (%)</Label>
                <Input
                  type="number"
                  value={slab.sellerPercentage}
                  disabled
                  className="mt-1 bg-gray-50"
                />
              </div>
            </div>
          )}
        </div>

        {/* Maximum Charges */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Set maximum delivery charges (optional)</Label>
          <p className="text-xs text-gray-500">You can set a cap on either customer charges OR your charges, not both</p>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs">Max customer charge (₹)</Label>
              <Input
                type="number"
                min="0"
                value={slab.maxBuyerDeliveryCharge || ''}
                onChange={(e) => onUpdateMaxCharge('buyer', Number(e.target.value) || 0)}
                disabled={slab.maxSellerDeliveryCharge > 0}
                className="mt-1"
                placeholder="No limit"
              />
            </div>
            <div>
              <Label className="text-xs">Max your charge (₹)</Label>
              <Input
                type="number"
                min="0"
                value={slab.maxSellerDeliveryCharge || ''}
                onChange={(e) => onUpdateMaxCharge('seller', Number(e.target.value) || 0)}
                disabled={slab.maxBuyerDeliveryCharge > 0}
                className="mt-1"
                placeholder="No limit"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function DeliveryConfigCard({
  config,
  onDelete,
  onStatusToggle,
}: {
  config: dclistingResponse;
  onDelete: (configId: number) => void;
  onStatusToggle: (config: dclistingResponse) => void;
}) {
  return (
    <div className="p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100">
      <div className="mb-3">
        <div className="flex flex-row justify-between items-center">
          <div className="p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]">
            <h3 className="text-base font-semibold text-typography-400">
              Config #{config.id}
            </h3>
          </div>

          <span
            className={`px-2 py-1 rounded-md text-xs font-medium ${config.active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
              }`}
          >
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>
        <p className="mt-2 text-sm text-typography-500">
          {config.configType === ConfigType.ORDER_VALUE_BASED
            ? "Order value based delivery configuration"
            : config.configType === ConfigType.PERCENTAGE_BASED
              ? "Percentage based delivery configuration" : "Unknown configuration type"}
        </p>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="my-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Order Range</p>
            <p className="text-sm font-medium text-typography-700">
              ₹{config.minOrderValue} - ₹{config.maxOrderValue}
            </p>
          </div>
          {
            config.maxSellerDeliveryCharge && config.maxSellerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Seller Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxSellerDeliveryCharge}
                </p>
              </div>
            ) : config.maxBuyerDeliveryCharge && config.maxBuyerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Buyer Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxBuyerDeliveryCharge}
                </p>
              </div>
            ) : null
          }
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Buyer Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.buyerPercentage}%
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Seller Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.sellerPercentage}%
            </p>
          </div>
        </div>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="relative mt-3 flex flex-row gap-3 justify-between items-center">
        <div className="flex items-center gap-2">
          <Switch
            checked={config.active}
            onCheckedChange={() => onStatusToggle(config)}
          />
          <span className="text-sm text-typography-600">
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>

        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
          onClick={() => onDelete(config.id)}
          aria-label="Delete delivery configuration"
        >
          <Trash2 className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
}

export default function DeliveryConfig() {
  const { data: deliveryConfigs } = useLoaderData<Loaderdata>();
  const { revalidate } = useRevalidator();
  const [showForm, setShowForm] = useState(false);
  const [slabs, setSlabs] = useState<SlabConfig[]>([]);

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();

  const { showToast } = useToast();

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Delivery Configuration deleted successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Failed to delete Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Delivery ranges created successfully", "success");
        setShowForm(false);
        setSlabs([]);
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Failed to create delivery ranges", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Delivery Configuration status updated successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Failed to update Delivery Configuration status", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  // Initialize slabs when form is opened
  useEffect(() => {
    if (showForm && slabs.length === 0) {
      initializeSlabs();
    }
  }, [showForm]);

  const handleDelete = (configId: number) => {
    if (confirm("Are you sure you want to delete this delivery configuration?")) {
      const formData = new FormData();
      formData.append("actionType", "deleteDeliveryConfig");
      formData.append("configId", configId.toString());
      fetcher.submit(formData, { method: "POST" });
    }
  };

  const handleStatusToggle = (config: dclistingResponse) => {
    const formData = new FormData();
    formData.append("actionType", "updateStatus");
    formData.append("config", JSON.stringify(config));
    fetcher.submit(formData, { method: "POST" });
  };

  // Helper functions for delivery range management
  const generateSlabId = () => `range_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  const initializeSlabs = () => {
    const firstSlab: SlabConfig = {
      id: generateSlabId(),
      minOrderValue: 0,
      maxOrderValue: 10000,
      buyerPercentage: 100,
      sellerPercentage: 0,
      maxBuyerDeliveryCharge: 0,
      maxSellerDeliveryCharge: 0,
      percentageOption: 'buyer-pays'
    };
    setSlabs([firstSlab]);
  };

  const updateSlabMaxValue = (id: string, newMaxValue: number) => {
    const updatedSlabs = slabs.map(slab =>
      slab.id === id ? { ...slab, maxOrderValue: newMaxValue } : slab
    );

    // If the new max value is less than 10000, create a new slab
    if (newMaxValue < 10000) {
      const newSlab: SlabConfig = {
        id: generateSlabId(),
        minOrderValue: newMaxValue + 1,
        maxOrderValue: 10000,
        buyerPercentage: 100,
        sellerPercentage: 0,
        maxBuyerDeliveryCharge: 0,
        maxSellerDeliveryCharge: 0,
        percentageOption: 'buyer-pays'
      };
      updatedSlabs.push(newSlab);
    }

    setSlabs(updatedSlabs);
  };

  const updateSlabPercentage = (id: string, option: 'buyer-pays' | 'seller-pays' | 'custom', customBuyer?: number) => {
    setSlabs(slabs.map(slab => {
      if (slab.id === id) {
        switch (option) {
          case 'buyer-pays':
            return { ...slab, buyerPercentage: 100, sellerPercentage: 0, percentageOption: option };
          case 'seller-pays':
            return { ...slab, buyerPercentage: 0, sellerPercentage: 100, percentageOption: option };
          case 'custom':
            const buyerPct = customBuyer || 50;
            return { ...slab, buyerPercentage: buyerPct, sellerPercentage: 100 - buyerPct, percentageOption: option };
          default:
            return slab;
        }
      }
      return slab;
    }));
  };

  const updateSlabMaxCharge = (id: string, field: 'buyer' | 'seller', value: number) => {
    setSlabs(slabs.map(slab => {
      if (slab.id === id) {
        if (field === 'buyer') {
          return { ...slab, maxBuyerDeliveryCharge: value, maxSellerDeliveryCharge: value > 0 ? 0 : slab.maxSellerDeliveryCharge };
        } else {
          return { ...slab, maxSellerDeliveryCharge: value, maxBuyerDeliveryCharge: value > 0 ? 0 : slab.maxBuyerDeliveryCharge };
        }
      }
      return slab;
    }));
  };

  const validateSlabs = (): string | null => {
    if (slabs.length === 0) return "Please create at least one delivery range";

    // Check if ranges cover 0 to 10000
    const sortedSlabs = [...slabs].sort((a, b) => a.minOrderValue - b.minOrderValue);

    if (sortedSlabs[0].minOrderValue !== 0) {
      return "First range must start from ₹0";
    }

    if (sortedSlabs[sortedSlabs.length - 1].maxOrderValue !== 10000) {
      return "Last range must end at ₹10,000";
    }

    // Check for gaps or overlaps
    for (let i = 0; i < sortedSlabs.length - 1; i++) {
      if (sortedSlabs[i].maxOrderValue + 1 !== sortedSlabs[i + 1].minOrderValue) {
        return "There should be no gaps between delivery ranges";
      }
    }

    return null;
  };

  const handleAddConfig = () => {
    const validationError = validateSlabs();
    if (validationError) {
      showToast(validationError, "error");
      return;
    }

    const formData = new FormData();
    formData.append("actionType", "addDeliveryConfig");
    formData.append("slabs", JSON.stringify(slabs));
    fetcher.submit(formData, { method: "POST" });
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Delivery Charge Configurations</h1>
        <p className="text-gray-600 mt-2">Manage your delivery charge configurations</p>
      </div>

      <div
        aria-labelledby="delivery-configs-list"
        className="pb-20 md:pb-5"
      >
        {deliveryConfigs && deliveryConfigs.length > 0 ? (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {deliveryConfigs.map((config) => (
                <DeliveryConfigCard
                  key={config.id}
                  config={config}
                  onDelete={handleDelete}
                  onStatusToggle={handleStatusToggle}
                />
              ))}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <div className="w-full max-w-4xl">
              <div className="text-center mb-8">
                <Truck className="h-16 w-16 text-gray-400 mx-auto" />
              </div>

              {!showForm ? (
                <div className="text-center">
                  <h3 className="text-2xl font-semibold text-foreground mb-2">
                    Create Your First Delivery Configuration
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Set up your delivery charge configuration to manage how delivery costs are split between you and your customers.
                  </p>
                  <Button
                    onClick={() => setShowForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Delivery Configuration
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">

                  {/* Delivery Range Cards */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {slabs.map((slab, index) => (
                      <DeliveryRangeCard
                        key={slab.id}
                        slab={slab}
                        index={index}
                        isLast={index === slabs.length - 1}
                        onUpdateMaxValue={(newMaxValue) => updateSlabMaxValue(slab.id, newMaxValue)}
                        onUpdatePercentage={(option, customBuyer) => updateSlabPercentage(slab.id, option, customBuyer)}
                        onUpdateMaxCharge={(field, value) => updateSlabMaxCharge(slab.id, field, value)}
                      />
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      onClick={handleAddConfig}
                      disabled={fetcher.state === "submitting" || fetcher.state === "loading"}
                      className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
                    >
                      {fetcher.state === "submitting" ? "Saving..." : "Save Delivery Ranges"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowForm(false);
                        setSlabs([]);
                      }}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
