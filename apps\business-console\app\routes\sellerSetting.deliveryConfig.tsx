
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Switch } from "~/components/ui/switch"
import { Trash2, Plus, Truck } from "lucide-react"
import { json, ActionFunction } from "@remix-run/node"
import { useFetcher, useLoaderData, useRevalidator } from "@remix-run/react"
import { withAuth, withResponse } from "~/utils/auth-utils"
import { LoaderFunction } from "@remix-run/node"
import { useToast } from "~/components/ui/ToastProvider";
import { getDeliveryConfigs, createDcConfig, deleteDeliveryConfig } from "~/services/deliveryConfigService"
import { dclistingResponse, DcBody, ConfigType } from "~/types/api/businessConsoleService/DeliveryConfig"

interface Loaderdata {
  data: dclistingResponse[];
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const sellerId = user?.userDetails?.sellerId;
  try {
    if (!sellerId) {
      throw new Response("Seller ID is required", { status: 400 });
    }
    const response = await getDeliveryConfigs(sellerId, request);
    return withResponse({ data: response.data?.data }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to fetch coupons data", {
      status: 500,
    });
  }
});

export const action = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId = user?.userDetails?.sellerId;

  if (!sellerId) {
    return json(
      { data: null, actionType: actionType, success: false, error: "Seller ID is required" },
      { status: 400 }
    );
  }

  if (actionType === "deleteDeliveryConfig") {
    const configId = Number(formData.get("configId"));
    try {
      const response = await deleteDeliveryConfig(configId, request);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "updateStatus") {
    const config = JSON.parse(formData.get("config") as string);

    try {
      // Get current config data first, then update only the active status
      const payload: Partial<DcBody> = {
        ...config,
        active: !config.active
      };

      const response = await createDcConfig(payload, request, config.id);
      return withResponse(
        { data: response.data, actionType: actionType, success: true },
        response.headers
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  if (actionType === "addDeliveryConfig") {
    const slabChangeAt = Number(formData.get("slabChangeAt"));
    const lowerSlabCapAt = Number(formData.get("lowerSlabCapAt"));
    const higherSlabCapAt = Number(formData.get("higherSlabCapAt"));

    // Create two payloads
    const payload1: Partial<DcBody> = {
      sellerId: sellerId,
      configType: ConfigType.ORDER_VALUE_BASED,
      minOrderValue: 0,
      maxOrderValue: slabChangeAt,
      maxBuyerDeliveryCharge: lowerSlabCapAt,
      // maxSellerDeliveryCharge: 0,
      buyerPercentage: 100,
      sellerPercentage: 0,
      active: true
    };

    const payload2: Partial<DcBody> = {
      sellerId: sellerId,
      configType: ConfigType.ORDER_VALUE_BASED,
      minOrderValue: slabChangeAt + 1,
      maxOrderValue: 10000,
      maxBuyerDeliveryCharge: higherSlabCapAt,
      buyerPercentage: 100,
      sellerPercentage: 0,
      active: true
    };

    try {
      // Create first configuration
      await createDcConfig(payload1, request);
      // Create second configuration
      await createDcConfig(payload2, request);

      return json(
        { data: null, actionType: actionType, success: true },
        { status: 200 }
      );
    } catch (error) {
      console.error("Error in action:", error);
      return json(
        { data: null, actionType: actionType, success: false },
        { status: 500 }
      );
    }
  }

  console.log("Invalid action type:", actionType);
  return json(
    { data: null, actionType: actionType, success: false },
    { status: 400 }
  );
});

export function DeliveryConfigCard({
  config,
  onDelete,
  onStatusToggle,
}: {
  config: dclistingResponse;
  onDelete: (configId: number) => void;
  onStatusToggle: (config: dclistingResponse) => void;
}) {
  return (
    <div className="p-4 rounded-xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.15)] transition-shadow duration-200 bg-white border border-neutral-100">
      <div className="mb-3">
        <div className="flex flex-row justify-between items-center">
          <div className="p-1 border border-neutral-600 rounded-md bg-[#FCFCFD]">
            <h3 className="text-base font-semibold text-typography-400">
              Config #{config.id}
            </h3>
          </div>

          <span
            className={`px-2 py-1 rounded-md text-xs font-medium ${config.active
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
              }`}
          >
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>
        <p className="mt-2 text-sm text-typography-500">
          {config.configType === ConfigType.ORDER_VALUE_BASED
            ? "Order value based delivery configuration"
            : config.configType === ConfigType.PERCENTAGE_BASED
              ? "Percentage based delivery configuration" : "Unknown configuration type"}
        </p>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="my-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Order Range</p>
            <p className="text-sm font-medium text-typography-700">
              ₹{config.minOrderValue} - ₹{config.maxOrderValue}
            </p>
          </div>
          {
            config.maxSellerDeliveryCharge && config.maxSellerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Seller Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxSellerDeliveryCharge}
                </p>
              </div>
            ) : config.maxBuyerDeliveryCharge && config.maxBuyerDeliveryCharge > 0 ? (
              <div className="text-right">
                <p className="text-xs text-typography-400 mb-1">Max Buyer Charge</p>
                <p className="text-sm font-medium text-typography-700">
                  ₹{config.maxBuyerDeliveryCharge}
                </p>
              </div>
            ) : null
          }
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-typography-400 mb-1">Buyer Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.buyerPercentage}%
            </p>
          </div>
          <div className="text-right">
            <p className="text-xs text-typography-400 mb-1">Seller Share</p>
            <p className="text-sm font-medium text-typography-700">
              {config.sellerPercentage}%
            </p>
          </div>
        </div>
      </div>

      <div className="border-b border-neutral-200" />

      <div className="relative mt-3 flex flex-row gap-3 justify-between items-center">
        <div className="flex items-center gap-2">
          <Switch
            checked={config.active}
            onCheckedChange={() => onStatusToggle(config)}
          />
          <span className="text-sm text-typography-600">
            {config.active ? "Active" : "Inactive"}
          </span>
        </div>

        <button
          className="border border-neutral-200 rounded-full p-2 cursor-pointer hover:bg-red-50 hover:border-red-200 transition-colors duration-200"
          onClick={() => onDelete(config.id)}
          aria-label="Delete delivery configuration"
        >
          <Trash2 className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
}

export default function DeliveryConfig() {
  const { data: deliveryConfigs } = useLoaderData<Loaderdata>();
  const { revalidate } = useRevalidator();
  const [showForm, setShowForm] = useState(false);
  const [slabChangeAt, setSlabChangeAt] = useState(400);
  const [lowerSlabCapAt, setLowerSlabCapAt] = useState(60);
  const [higherSlabCapAt, setHigherSlabCapAt] = useState(30);

  const fetcher = useFetcher<{
    data: void;
    success: boolean;
    actionType: string;
  }>();

  const { showToast } = useToast();

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Delivery Configuration deleted successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "deleteDeliveryConfig"
      ) {
        showToast("Failed to delete Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Delivery Configuration created successfully", "success");
        setShowForm(false);
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "addDeliveryConfig"
      ) {
        showToast("Failed to create Delivery Configuration", "error");
      } else if (
        fetcher.data.success === true &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Delivery Configuration status updated successfully", "success");
        revalidate();
      } else if (
        fetcher.data.success === false &&
        fetcher.data.actionType === "updateStatus"
      ) {
        showToast("Failed to update Delivery Configuration status", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  const handleDelete = (configId: number) => {
    if (confirm("Are you sure you want to delete this delivery configuration?")) {
      const formData = new FormData();
      formData.append("actionType", "deleteDeliveryConfig");
      formData.append("configId", configId.toString());
      fetcher.submit(formData, { method: "POST" });
    }
  };

  const handleStatusToggle = (config: dclistingResponse) => {
    const formData = new FormData();
    formData.append("actionType", "updateStatus");
    formData.append("config", JSON.stringify(config));
    fetcher.submit(formData, { method: "POST" });
  };

  const handleAddConfig = () => {
    const formData = new FormData();
    formData.append("actionType", "addDeliveryConfig");
    formData.append("slabChangeAt", slabChangeAt.toString());
    formData.append("lowerSlabCapAt", lowerSlabCapAt.toString());
    formData.append("higherSlabCapAt", higherSlabCapAt.toString());
    fetcher.submit(formData, { method: "POST" });
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Delivery Charge Configurations</h1>
        <p className="text-gray-600 mt-2">Manage your delivery charge configurations</p>
      </div>

      <div
        aria-labelledby="delivery-configs-list"
        className="pb-20 md:pb-5"
      >
        {deliveryConfigs && deliveryConfigs.length > 0 ? (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {deliveryConfigs.map((config) => (
                <DeliveryConfigCard
                  key={config.id}
                  config={config}
                  onDelete={handleDelete}
                  onStatusToggle={handleStatusToggle}
                />
              ))}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 text-center">
            <div className="w-full max-w-4xl">
              <div className="text-center mb-8">
                <Truck className="h-16 w-16 text-gray-400 mx-auto" />
              </div>

              {!showForm ? (
                <div className="text-center">
                  <h3 className="text-2xl font-semibold text-foreground mb-2">
                    Create Your First Delivery Configuration
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Set up your delivery charge configuration to manage how delivery costs are split between you and your customers.
                  </p>
                  <Button
                    onClick={() => setShowForm(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Delivery Configuration
                  </Button>
                </div>
              ) : (
                <Card className="text-left">
                  <CardHeader>
                    <CardTitle className="text-lg">Configure Delivery Charges</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="slabChangeAt" className="text-sm font-medium">
                        Order Value Threshold (₹)
                      </Label>
                      <Input
                        id="slabChangeAt"
                        type="number"
                        value={slabChangeAt}
                        onChange={(e) => setSlabChangeAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="400"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Orders above this amount will have lower delivery charges
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="lowerSlabCapAt" className="text-sm font-medium">
                        Max Delivery Charge for Orders Below Threshold (₹)
                      </Label>
                      <Input
                        id="lowerSlabCapAt"
                        type="number"
                        value={lowerSlabCapAt}
                        onChange={(e) => setLowerSlabCapAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="60"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum amount customers pay for orders under ₹{slabChangeAt}
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="higherSlabCapAt" className="text-sm font-medium">
                        Max Delivery Charge for Orders Above Threshold (₹)
                      </Label>
                      <Input
                        id="higherSlabCapAt"
                        type="number"
                        value={higherSlabCapAt}
                        onChange={(e) => setHigherSlabCapAt(Number(e.target.value))}
                        className="mt-1"
                        placeholder="30"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum amount customers pay for orders above ₹{slabChangeAt}
                      </p>
                    </div>

                    <div className="bg-blue-50 p-3 rounded-lg">
                      <h4 className="text-sm font-medium text-blue-900 mb-2">How it works:</h4>
                      <ul className="text-xs text-blue-800 space-y-1">
                        <li>• Orders under ₹{slabChangeAt}: Customer pays max ₹{lowerSlabCapAt}, you cover the rest</li>
                        <li>• Orders above ₹{slabChangeAt}: Customer pays max ₹{higherSlabCapAt}, you cover the rest</li>
                        <li>• This encourages larger orders while keeping delivery affordable</li>
                      </ul>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={handleAddConfig}
                        disabled={fetcher.state === "submitting" || fetcher.state === "loading"}
                        className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
                      >
                        {fetcher.state === "submitting" ? "Saving..." : "Save Configuration"}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowForm(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
