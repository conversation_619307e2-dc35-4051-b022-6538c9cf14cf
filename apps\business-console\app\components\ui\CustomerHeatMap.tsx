import type React from "react"
import { useState, useMemo, useCallback } from "react"
import { GoogleMap, Marker, Circle, HeatmapLayer, useLoadScript } from "@react-google-maps/api"
import { Card } from "~/components/ui/card"
import { MapPin, Users } from "lucide-react"

interface Customer {
  id: string
  name: string
  lat: number
  lng: number
  orderCount?: number
  lastOrderDate?: string
}

interface Shop {
  id: string
  name: string
  lat: number
  lng: number
}

interface CustomerHeatMapProps {
  shop: Shop
  customers: Customer[]
  radiusKm: number
}

const googleMapsApiKey = "AIzaSyDBh6D6NIEiH08bj01ybByaayfM1T7W6XY"
const libraries: ("places" | "geometry" | "visualization")[] = ["places", "visualization"]

const CustomerHeatMap: React.FC<CustomerHeatMapProps> = ({ shop, customers, radiusKm }) => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: googleMapsApiKey,
    libraries: libraries,
  })
  const [isMapReady, setIsMapReady] = useState(false);

  const mapContainerStyle = {
    width: "100%",
    height: "600px",
    borderRadius: "12px",
  }

  const center = useMemo(
    () => ({
      lat: shop.lat,
      lng: shop.lng,
    }),
    [shop.lat, shop.lng],
  )

  // Handle map load
  const onMapLoad = useCallback(() => {
    setTimeout(() => {
      setIsMapReady(true)
    }, 300)
  }, [])

  // Create heatmap data
  const heatmapData = useMemo(() => {
    if (!isMapReady || !google.maps.LatLng) {
      return []
    }
    return customers.map((customer) => new google.maps.LatLng(customer.lat, customer.lng))
  }, [customers, isMapReady])


  if (!isLoaded) {
    return (
      <Card className="p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading Map...</p>
        </div>
      </Card>
    )
  }

  if (loadError) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: {loadError.message}</p>
        </div>
      </Card>
    )
  }

  if (!shop || !shop.lat || !shop.lng) {
    return (
      <Card className="p-8">
        <div className="text-center text-red-500">
          <MapPin className="mx-auto mb-2 h-8 w-8" />
          <p>Error: Shop location data is missing</p>
        </div>
      </Card>
    )
  }

  if (!customers || customers.length === 0) {
    return (
      <Card className="p-8">
        <div className="text-center text-yellow-500">
          <Users className="mx-auto mb-2 h-8 w-8" />
          <p>No customer data available</p>
        </div>
      </Card>
    )
  }

  return (
    <div>
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={center}
        zoom={12}
        onLoad={onMapLoad}
        options={{
          streetViewControl: false
        }}
      >
        {isMapReady && (
          <>
            <Marker
              position={center}
              options={{
                title: shop.name,
              }}
            />
            <Circle
              center={center}
              radius={radiusKm * 1000}
              options={{
                fillColor: "#da61f2",
                fillOpacity: 0,
                strokeColor: "#da61f2",
                strokeOpacity: 0.8,
                strokeWeight: 2,
              }}
            />
            {heatmapData.length > 0 && (
              <HeatmapLayer
                data={heatmapData}
                options={{
                  radius: 20,
                  opacity: 0.6,
                }}
              />
            )}
          </>
        )}
      </GoogleMap>
    </div>
  )
}

export default CustomerHeatMap;
